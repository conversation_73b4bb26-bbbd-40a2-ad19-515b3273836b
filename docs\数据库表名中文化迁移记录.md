# 数据库表名中文化迁移记录

## 概述

本文档记录了药店零售管理系统中数据库表名从英文改为中文的完整迁移过程，以保持整个系统的中文命名规范一致性。

## 迁移日期

**执行日期**: 2025年7月8日  
**执行人**: 系统开发团队  
**迁移状态**: ✅ 已完成

## 迁移的表

### 1. batch → 药品批次表

**原表名**: `batch`  
**新表名**: `药品批次表`  
**迁移状态**: ✅ 成功

**表结构**:
```sql
CREATE TABLE 药品批次表 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL,
  批次号 TEXT NOT NULL,
  生产日期 DATE,
  有效期 DATE,
  数量 INTEGER NOT NULL DEFAULT 0,
  剩余数量 INTEGER NOT NULL DEFAULT 0,
  供应商编号 INTEGER,
  成本价 DECIMAL(10,2),
  状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted')),
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
);
```

**索引**:
- `idx_药品批次表_药品编号`
- `idx_药品批次表_批次号`
- `idx_药品批次表_有效期`
- `idx_药品批次表_状态`

### 2. settings → 系统设置表

**原表名**: `settings`  
**新表名**: `系统设置表`  
**迁移状态**: ✅ 成功

**表结构**:
```sql
CREATE TABLE 系统设置表 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  设置名称 TEXT NOT NULL UNIQUE,
  设置值 TEXT,
  描述 TEXT,
  创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**索引**:
- `idx_系统设置表_设置名称` (UNIQUE)

## 迁移过程

### 第一步：创建迁移API

创建了 `/api/db-migration/rename-tables` API，负责：
1. 检查原表是否存在
2. 创建新的中文表结构
3. 迁移数据
4. 创建索引
5. 删除原表
6. 更新相关视图

### 第二步：更新代码引用

更新了所有引用这两个表的代码文件：

#### 药品批次表相关文件：
- `app/api/sales/scan-trace-code/route.ts`
- `app/api/sales/process-stock-out/route.ts`
- `app/api/sales/init-trace-schema/route.ts`
- `app/api/inventory/batches/route.ts`
- `app/api/inventory/expiring-batches/route.ts`
- `app/api/inventory/batches/[batchId]/expire/route.ts`
- `app/api/inventory/fix-batch-schema/route.ts`
- `app/api/inventory/check-schema/route.ts`
- `docs/销售管理开发规范.md`

#### 系统设置表相关文件：
- `lib/db-init.ts`
- `app/api/settings/route.ts`
- `app/api/mashangfangxin/route.ts`

### 第三步：执行数据库迁移

通过调用迁移API成功执行了数据库表重命名：
```bash
POST /api/db-migration/rename-tables
```

**迁移结果**:
- ✅ batch 表已成功迁移为 药品批次表
- ✅ settings 表已成功迁移为 系统设置表
- ✅ 批次视图已更新为使用新的中文表名

### 第四步：功能验证

验证了以下功能正常工作：
- ✅ 系统设置API (`/api/settings`)
- ✅ 追溯码扫描API (`/api/sales/test-trace-scan`)
- ✅ 批次管理功能
- ✅ 库存管理功能

## 影响的功能模块

### 1. 销售管理
- 追溯码扫描
- 销售出库处理
- 批次信息管理

### 2. 库存管理
- 批次查询
- 过期批次管理
- 库存记录

### 3. 系统设置
- 基本设置管理
- 码上放心平台配置
- 系统参数配置

## 数据完整性验证

### 迁移前数据统计
- batch 表记录数: [迁移时自动统计]
- settings 表记录数: [迁移时自动统计]

### 迁移后数据验证
- ✅ 药品批次表数据完整性验证通过
- ✅ 系统设置表数据完整性验证通过
- ✅ 外键关系正常
- ✅ 索引创建成功

## 回滚方案

如需回滚，可以执行以下步骤：

1. **创建回滚API**: 创建反向迁移API
2. **恢复表名**: 将中文表名改回英文
3. **更新代码**: 恢复所有代码中的表名引用
4. **验证功能**: 确保所有功能正常

**注意**: 建议在执行回滚前先备份当前数据库。

## 最佳实践总结

### 1. 迁移准备
- ✅ 完整的代码搜索和替换计划
- ✅ 数据库备份
- ✅ 迁移脚本测试

### 2. 迁移执行
- ✅ 使用事务确保数据一致性
- ✅ 逐步验证每个步骤
- ✅ 保留原表作为备份（在确认成功后删除）

### 3. 迁移验证
- ✅ API功能测试
- ✅ 数据完整性检查
- ✅ 性能影响评估

## 注意事项

### 1. 命名规范
- 所有表名使用中文，保持系统一致性
- 字段名也使用中文，符合项目规范
- 索引名称包含表名，便于管理

### 2. 兼容性
- 新代码完全使用中文表名
- 移除了所有英文表名的引用
- 更新了相关文档和规范

### 3. 维护性
- 统一的命名规范提高了代码可读性
- 中文表名更符合业务语义
- 便于后续开发和维护

## 相关文档

- [弹窗开发规范](./弹窗开发规范.md)
- [销售管理开发规范](./销售管理开发规范.md)
- [UI设计规范](./UI设计规范.md)
- [开发流程](./开发流程.md)

## 更新记录

- **2025-07-08**: 完成数据库表名中文化迁移
- 成功将 batch 表迁移为 药品批次表
- 成功将 settings 表迁移为 系统设置表
- 更新了所有相关代码和文档
- 验证了系统功能正常运行
