import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';
import { ensureSettingsTable } from '@/lib/db-init';

// 获取系统设置
export async function GET() {
  try {
    // 确保系统设置表存在
    await ensureSettingsTable();

    const settings = await query('SELECT 设置名称 as setting_name, 设置值 as setting_value, 描述 as description FROM 系统设置表');

    // 将设置转换为键值对对象
    const settingsObject: Record<string, string> = {};
    settings.forEach((setting: any) => {
      settingsObject[setting.setting_name] = setting.setting_value;
    });

    return NextResponse.json({
      success: true,
      data: settingsObject
    });
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新系统设置
export async function POST(request: NextRequest) {
  try {
    // 确保settings表存在
    await ensureSettingsTable();

    const data = await request.json();

    // 验证必要的设置项
    if (!data.storeName) {
      return NextResponse.json(
        { success: false, message: '药店名称不能为空' },
        { status: 400 }
      );
    }

    // 验证订单编号位数
    const orderNumberDigits = parseInt(data.orderNumberDigits);
    if (isNaN(orderNumberDigits) || orderNumberDigits < 4 || orderNumberDigits > 10) {
      return NextResponse.json(
        { success: false, message: '订单编号位数必须在4-10之间' },
        { status: 400 }
      );
    }

    // 更新或插入每个设置项
    for (const [key, value] of Object.entries(data)) {
      // 跳过空值
      if (value === null || value === undefined) continue;

      // 检查设置项是否存在
      const existingSetting = await query(
        'SELECT 设置名称 FROM 系统设置表 WHERE 设置名称 = ?',
        [key]
      );

      if (existingSetting && existingSetting.length > 0) {
        // 更新现有设置
        await run(
          'UPDATE 系统设置表 SET 设置值 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE 设置名称 = ?',
          [value, key]
        );
      } else {
        // 插入新设置
        await run(
          'INSERT INTO 系统设置表 (设置名称, 设置值) VALUES (?, ?)',
          [key, value]
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: '系统设置已更新'
    });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 重置系统设置为默认值
export async function DELETE() {
  try {
    // 确保系统设置表存在
    await ensureSettingsTable();

    // 默认设置
    const defaultSettings = {
      storeName: '药店零售管理系统',
      orderNumberDigits: '4',
      receiptSize: '80mm',
      mashangfangxinAppkey: '',
      mashangfangxinAppsecret: '',
      mashangfangxinUrl: 'http://gw.api.taobao.com/router/rest',
      mashangfangxinRefEntId: ''
    };

    // 更新每个设置项为默认值
    for (const [key, value] of Object.entries(defaultSettings)) {
      await run(
        'UPDATE 系统设置表 SET 设置值 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE 设置名称 = ?',
        [value, key]
      );
    }

    return NextResponse.json({
      success: true,
      message: '系统设置已重置为默认值',
      data: defaultSettings
    });
  } catch (error) {
    console.error('重置系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '重置系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
